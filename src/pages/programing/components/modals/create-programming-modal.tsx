import { Button } from '@/components/shadcnui/button';
import { Checkbox } from '@/components/shadcnui/checkbox';
import { Label } from '@/components/shadcnui/label';
import { Input } from '@/components/shadcnui/input';
import { useFindAllDevices } from '@/pages/devices/hooks/crud/find-all.hook';
import { useFindAllPresentations } from '@/pages/presentations/hooks/crud/find-all.hook';
import { useCreateProgramming } from '@/pages/programing/hooks/create-programming.hook';
import { Modal, ModalBody, ModalContent, ModalFooter, CircularProgress } from '@nextui-org/react';
import { Calendar, Monitor, Tv, Search, CheckSquare, Square, CheckCircle2, ChevronLeft, ChevronRight, Edit3 } from 'lucide-react';
import { useState, useMemo } from 'react';
import { toast } from 'sonner';
import { GlobalFilter } from '@/components/global-search-filter';

interface ICreateProgrammingModalProps {
	isOpen: boolean;
	onClose: () => void;
	onOpenChange: (open: boolean) => void;
	backdrop?: 'transparent' | 'opaque' | 'blur';
}

export const CreateProgrammingModal = ({ isOpen, onClose, onOpenChange, backdrop = 'opaque' }: ICreateProgrammingModalProps) => {
	const [currentStep, setCurrentStep] = useState(1);
	const [selectedDevices, setSelectedDevices] = useState<string[]>([]);
	const [selectedPresentations, setSelectedPresentations] = useState<string[]>([]);
	const [deviceSearchTerm, setDeviceSearchTerm] = useState('');
	const [presentationSearchTerm, setPresentationSearchTerm] = useState('');

	const { data: devicesData, isLoading: devicesLoading } = useFindAllDevices();
	const { data: presentationsData, isLoading: presentationsLoading } = useFindAllPresentations();
	const { createProgramming, isLoading: isCreating } = useCreateProgramming(() => {
		handleClose();
	});

	const handleClose = () => {
		setCurrentStep(1);
		setSelectedDevices([]);
		setSelectedPresentations([]);
		setDeviceSearchTerm('');
		setPresentationSearchTerm('');
		onClose();
	};

	const handleNextStep = () => {
		if (currentStep < 3) {
			setCurrentStep(currentStep + 1);
		}
	};

	const handlePrevStep = () => {
		if (currentStep > 1) {
			setCurrentStep(currentStep - 1);
		}
	};

	const goToStep = (step: number) => {
		setCurrentStep(step);
	};

	const handleDeviceChange = (deviceId: string, checked: boolean) => {
		if (checked) {
			setSelectedDevices([...selectedDevices, deviceId]);
		} else {
			setSelectedDevices(selectedDevices.filter((id) => id !== deviceId));
		}
	};

	const handlePresentationChange = (presentationId: string, checked: boolean) => {
		if (checked) {
			setSelectedPresentations([...selectedPresentations, presentationId]);
		} else {
			setSelectedPresentations(selectedPresentations.filter((id) => id !== presentationId));
		}
	};

	// Funções para seleção em massa
	const handleSelectAllDevices = () => {
		setSelectedDevices(filteredDevices.map((device) => device.id));
	};

	const handleDeselectAllDevices = () => {
		setSelectedDevices([]);
	};

	const handleSelectAllPresentations = () => {
		setSelectedPresentations(filteredPresentations.map((presentation) => presentation.id));
	};

	const handleDeselectAllPresentations = () => {
		setSelectedPresentations([]);
	};

	const handleSubmit = async () => {
		if (selectedDevices.length === 0) {
			toast.error('Selecione pelo menos um dispositivo');
			return;
		}
		if (selectedPresentations.length === 0) {
			toast.error('Selecione pelo menos uma apresentação');
			return;
		}

		try {
			await createProgramming({
				data: {
					id_devices: selectedDevices,
					id_presentations: selectedPresentations,
				},
			});
		} catch (error) {
			console.error('Erro ao criar programação:', error);
		}
	};

	const devices = useMemo(() => {
		return devicesData?.success ? devicesData.data.data : [];
	}, [devicesData]);

	const presentations = useMemo(() => {
		return presentationsData?.success ? presentationsData.data.data : [];
	}, [presentationsData]);

	const filteredDevices = useMemo(() => {
		return devices.filter((device) => device.name.toLowerCase().includes(deviceSearchTerm.toLowerCase()));
	}, [devices, deviceSearchTerm]);

	const filteredPresentations = useMemo(() => {
		return presentations.filter((presentation) => presentation.title.toLowerCase().includes(presentationSearchTerm.toLowerCase()));
	}, [presentations, presentationSearchTerm]);

	const allDevicesSelected = filteredDevices.length > 0 && filteredDevices.every((device) => selectedDevices.includes(device.id));
	const allPresentationsSelected =
		filteredPresentations.length > 0 && filteredPresentations.every((presentation) => selectedPresentations.includes(presentation.id));
	const canCreateProgramming = selectedDevices.length > 0 && selectedPresentations.length > 0;
	const canAdvanceFromStep1 = selectedDevices.length > 0;
	const canAdvanceFromStep2 = selectedPresentations.length > 0;

	const getStepTitle = (step: number) => {
		switch (step) {
			case 1:
				return 'Selecionar Dispositivos';
			case 2:
				return 'Selecionar Apresentações';
			case 3:
				return 'Revisar e Confirmar';
			default:
				return 'Nova Programação';
		}
	};

	const getStepDescription = (step: number) => {
		switch (step) {
			case 1:
				return 'Escolha os dispositivos que receberão as apresentações';
			case 2:
				return 'Selecione as apresentações que serão exibidas';
			case 3:
				return 'Revise suas seleções e confirme a criação da programação';
			default:
				return 'Selecione os dispositivos e apresentações para criar uma nova programação';
		}
	};

	return (
		<Modal isOpen={isOpen} onOpenChange={onOpenChange} backdrop={backdrop} size="xl" className="rounded-xl border border-[#232728] bg-muted shadow-lg">
			<ModalContent>
				{() => (
					<>
						{/* Header com indicadores de progresso */}
						<div className="flex flex-col gap-4 p-6 pb-0">
							<div className="flex items-center gap-2">
								<Calendar className="h-6 w-6 text-primary" />
								<h1 className="text-lg font-semibold text-white">Nova Programação</h1>
							</div>

							{/* Indicador de progresso */}
							<div className="flex items-center justify-center">
								<div className="flex items-center">
									{[1, 2, 3].map((step) => (
										<div key={step} className="flex items-center">
											<div
												className={`flex h-8 w-8 cursor-pointer items-center justify-center rounded-full text-sm font-medium transition-all ${
													step === currentStep
														? 'bg-primary text-primary-foreground'
														: step < currentStep
															? 'bg-primary/80 text-primary-foreground'
															: 'bg-muted-foreground/20 text-muted-foreground'
												}`}
												onClick={() => {
													// Permite navegar para steps anteriores ou para o step 2 se step 1 está completo
													if (step < currentStep || (step === 2 && canAdvanceFromStep1) || step === currentStep) {
														goToStep(step);
													}
												}}
											>
												{step < currentStep ? <CheckCircle2 className="h-4 w-4" /> : step}
											</div>
											{step < 3 && (
												<div
													className={`mx-4 h-0.5 w-16 transition-all ${
														step < currentStep ? 'bg-primary' : 'bg-muted-foreground/20'
													}`}
												/>
											)}
										</div>
									))}
								</div>
							</div>

							{/* Título e descrição do step atual */}
							<div className="text-center">
								<h2 className="text-base font-semibold text-white">{getStepTitle(currentStep)}</h2>
								<p className="text-sm text-muted-foreground">{getStepDescription(currentStep)}</p>
							</div>
						</div>

						<ModalBody className="pb-6 pt-2">
							{/* Step 1: Seleção de Dispositivos */}
							{currentStep === 1 && (
								<div className="space-y-4">
									<div className="flex items-center justify-between">
										<div className="flex items-center gap-2">
											<Tv className="h-5 w-5 text-primary" />
											<span className="text-sm font-medium text-white">Dispositivos Disponíveis</span>
											{selectedDevices.length > 0 && (
												<span className="ml-2 rounded-full bg-primary/20 px-2 py-1 text-xs text-primary">
													{selectedDevices.length} selecionado{selectedDevices.length > 1 ? 's' : ''}
												</span>
											)}
										</div>
										{devices.length > 0 && (
											<Button
												variant="ghost"
												size="sm"
												onClick={allDevicesSelected ? handleDeselectAllDevices : handleSelectAllDevices}
												className="h-7 px-2 text-xs"
											>
												{allDevicesSelected ? (
													<>
														<Square className="mr-1 h-3 w-3" />
														Deselecionar todos
													</>
												) : (
													<>
														<CheckSquare className="mr-1 h-3 w-3" />
														Selecionar todos
													</>
												)}
											</Button>
										)}
									</div>

									{devices.length > 0 && (
										<div className="relative flex w-full items-center justify-center">
											<GlobalFilter
												globalFilter={deviceSearchTerm}
												setGlobalFilter={setDeviceSearchTerm}
												placeholder="Buscar dispositivos..."
											/>
										</div>
									)}

									{devicesLoading ? (
										<div className="flex items-center justify-center py-12">
											<CircularProgress size="sm" />
											<span className="ml-2 text-sm text-muted-foreground">Carregando dispositivos...</span>
										</div>
									) : devices.length === 0 ? (
										<div className="flex flex-col items-center justify-center py-12 text-center">
											<Tv className="h-12 w-12 text-muted-foreground/50" />
											<p className="mt-2 text-sm text-muted-foreground">Nenhum dispositivo encontrado</p>
											<p className="text-xs text-muted-foreground">Cadastre dispositivos antes de criar uma programação</p>
										</div>
									) : filteredDevices.length === 0 ? (
										<div className="flex flex-col items-center justify-center py-12 text-center">
											<Search className="h-8 w-8 text-muted-foreground/50" />
											<p className="mt-2 text-sm text-muted-foreground">Nenhum dispositivo encontrado</p>
											<p className="text-xs text-muted-foreground">Tente ajustar os termos de busca</p>
										</div>
									) : (
										<div className="max-h-64 w-full overflow-y-auto rounded-lg border border-border bg-muted/30 p-4">
											<div className="grid gap-3 sm:grid-cols-2">
												{filteredDevices.map((device) => (
													<div
														key={device.id}
														className={`flex items-center space-x-3 rounded-lg p-3 transition-all duration-200 ${
															selectedDevices.includes(device.id)
																? 'border border-primary/20 bg-primary/10 shadow-sm'
																: 'bg-background/50 hover:bg-background/70'
														}`}
													>
														<Checkbox
															id={`device-${device.id}`}
															checked={selectedDevices.includes(device.id)}
															onCheckedChange={(checked) => handleDeviceChange(device.id, checked as boolean)}
														/>
														<Label
															htmlFor={`device-${device.id}`}
															className="flex-1 cursor-pointer text-sm font-medium"
														>
															{device.name}
														</Label>
														{selectedDevices.includes(device.id) && <CheckCircle2 className="h-4 w-4 text-primary" />}
													</div>
												))}
											</div>
										</div>
									)}
								</div>
							)}

							{/* Step 2: Seleção de Apresentações */}
							{currentStep === 2 && (
								<div className="space-y-4">
									<div className="flex items-center justify-between">
										<div className="flex items-center gap-2">
											<Monitor className="h-5 w-5 text-primary" />
											<span className="text-sm font-medium text-white">Apresentações Disponíveis</span>
											{selectedPresentations.length > 0 && (
												<span className="ml-2 rounded-full bg-primary/20 px-2 py-1 text-xs text-primary">
													{selectedPresentations.length} selecionada{selectedPresentations.length > 1 ? 's' : ''}
												</span>
											)}
										</div>
										{presentations.length > 0 && (
											<Button
												variant="ghost"
												size="sm"
												onClick={allPresentationsSelected ? handleDeselectAllPresentations : handleSelectAllPresentations}
												className="h-7 px-2 text-xs"
											>
												{allPresentationsSelected ? (
													<>
														<Square className="mr-1 h-3 w-3" />
														Deselecionar todas
													</>
												) : (
													<>
														<CheckSquare className="mr-1 h-3 w-3" />
														Selecionar todas
													</>
												)}
											</Button>
										)}
									</div>

									{presentations.length > 0 && (
										<div className="relative flex w-full items-center justify-center">
											<GlobalFilter
												globalFilter={presentationSearchTerm}
												setGlobalFilter={setPresentationSearchTerm}
												placeholder="Buscar apresentações..."
											/>
										</div>
									)}

									{presentationsLoading ? (
										<div className="flex items-center justify-center py-12">
											<CircularProgress size="sm" />
											<span className="ml-2 text-sm text-muted-foreground">Carregando apresentações...</span>
										</div>
									) : presentations.length === 0 ? (
										<div className="flex flex-col items-center justify-center py-12 text-center">
											<Monitor className="h-12 w-12 text-muted-foreground/50" />
											<p className="mt-2 text-sm text-muted-foreground">Nenhuma apresentação encontrada</p>
											<p className="text-xs text-muted-foreground">Crie apresentações antes de configurar uma programação</p>
										</div>
									) : filteredPresentations.length === 0 ? (
										<div className="flex flex-col items-center justify-center py-12 text-center">
											<Search className="h-8 w-8 text-muted-foreground/50" />
											<p className="mt-2 text-sm text-muted-foreground">Nenhuma apresentação encontrada</p>
											<p className="text-xs text-muted-foreground">Tente ajustar os termos de busca</p>
										</div>
									) : (
										<div className="max-h-64 w-full overflow-y-auto rounded-lg border border-border bg-muted/30 p-4">
											<div className="grid gap-3 sm:grid-cols-2">
												{filteredPresentations.map((presentation) => (
													<div
														key={presentation.id}
														className={`flex items-center space-x-3 rounded-lg p-3 transition-all duration-200 ${
															selectedPresentations.includes(presentation.id)
																? 'border border-primary/20 bg-primary/10 shadow-sm'
																: 'bg-background/50 hover:bg-background/70'
														}`}
													>
														<Checkbox
															id={`presentation-${presentation.id}`}
															checked={selectedPresentations.includes(presentation.id)}
															onCheckedChange={(checked) =>
																handlePresentationChange(presentation.id, checked as boolean)
															}
														/>
														<Label
															htmlFor={`presentation-${presentation.id}`}
															className="flex-1 cursor-pointer text-sm font-medium"
														>
															{presentation.title}
														</Label>
														{selectedPresentations.includes(presentation.id) && (
															<CheckCircle2 className="h-4 w-4 text-primary" />
														)}
													</div>
												))}
											</div>
										</div>
									)}
								</div>
							)}

							{/* Step 3: Revisão e Confirmação */}
							{currentStep === 3 && (
								<div className="space-y-6">
									{/* Resumo dos Dispositivos */}
									<div className="rounded-lg border border-border bg-muted/20 p-4">
										<div className="mb-3 flex items-center justify-between">
											<div className="flex items-center gap-2">
												<Tv className="h-4 w-4 text-primary" />
												<span className="text-sm font-medium text-white">Dispositivos Selecionados</span>
												<span className="rounded-full bg-primary/20 px-2 py-1 text-xs text-primary">
													{selectedDevices.length}
												</span>
											</div>
											<Button variant="ghost" size="sm" onClick={() => goToStep(1)} className="h-7 px-2 text-xs">
												<Edit3 className="mr-1 h-3 w-3" />
												Editar
											</Button>
										</div>
										<div className="grid max-h-32 gap-2 overflow-y-auto">
											{devices
												.filter((device) => selectedDevices.includes(device.id))
												.map((device) => (
													<div key={device.id} className="flex items-center gap-2 text-sm">
														<CheckCircle2 className="h-3 w-3 text-primary" />
														<span className="text-muted-foreground">{device.name}</span>
													</div>
												))}
										</div>
									</div>

									{/* Resumo das Apresentações */}
									<div className="rounded-lg border border-border bg-muted/20 p-4">
										<div className="mb-3 flex items-center justify-between">
											<div className="flex items-center gap-2">
												<Monitor className="h-4 w-4 text-primary" />
												<span className="text-sm font-medium text-white">Apresentações Selecionadas</span>
												<span className="rounded-full bg-primary/20 px-2 py-1 text-xs text-primary">
													{selectedPresentations.length}
												</span>
											</div>
											<Button variant="ghost" size="sm" onClick={() => goToStep(2)} className="h-7 px-2 text-xs">
												<Edit3 className="mr-1 h-3 w-3" />
												Editar
											</Button>
										</div>
										<div className="grid max-h-32 gap-2 overflow-y-auto">
											{presentations
												.filter((presentation) => selectedPresentations.includes(presentation.id))
												.map((presentation) => (
													<div key={presentation.id} className="flex items-center gap-2 text-sm">
														<CheckCircle2 className="h-3 w-3 text-primary" />
														<span className="text-muted-foreground">{presentation.title}</span>
													</div>
												))}
										</div>
									</div>

									{/* Informação final */}
									<div className="rounded-lg border border-primary/20 bg-primary/5 p-4">
										<div className="mb-2 flex items-center gap-2">
											<CheckCircle2 className="h-4 w-4 text-primary" />
											<span className="text-sm font-medium text-white">Pronto para criar a programação</span>
										</div>
										<p className="text-xs text-muted-foreground">
											Esta programação será aplicada a {selectedDevices.length} dispositivo
											{selectedDevices.length > 1 ? 's' : ''} e exibirá {selectedPresentations.length} apresentação
											{selectedPresentations.length > 1 ? 'ões' : ''}.
										</p>
									</div>
								</div>
							)}
						</ModalBody>

						{/* Footer com botões de navegação */}
						<ModalFooter className="flex justify-between border-t border-border pt-4">
							<div className="flex gap-2">
								{/* Botão Cancelar - sempre visível */}
								<Button variant="ghost" onClick={handleClose} disabled={isCreating}>
									Cancelar
								</Button>
							</div>

							<div className="flex gap-2">
								{/* Botão Voltar - apenas nos steps 2 e 3 */}
								{currentStep > 1 && (
									<Button variant="outline" onClick={handlePrevStep} disabled={isCreating} className="flex items-center gap-2">
										<ChevronLeft className="h-4 w-4" />
										Voltar
									</Button>
								)}

								{/* Botão Próximo - steps 1 e 2 */}
								{currentStep < 3 && (
									<Button
										onClick={handleNextStep}
										disabled={
											isCreating || (currentStep === 1 && !canAdvanceFromStep1) || (currentStep === 2 && !canAdvanceFromStep2)
										}
										className="flex items-center gap-2"
									>
										{currentStep === 1 ? 'Próximo' : 'Revisar'}
										<ChevronRight className="h-4 w-4" />
									</Button>
								)}

								{/* Botão Criar Programação - apenas no step 3 */}
								{currentStep === 3 && (
									<Button onClick={handleSubmit} disabled={isCreating || !canCreateProgramming} className="flex items-center gap-2">
										{isCreating ? (
											<>
												<CircularProgress size="sm" className="h-4 w-4" />
												Criando...
											</>
										) : (
											<>
												<CheckCircle2 className="h-4 w-4" />
												Criar Programação
											</>
										)}
									</Button>
								)}
							</div>
						</ModalFooter>
					</>
				)}
			</ModalContent>
		</Modal>
	);
};
