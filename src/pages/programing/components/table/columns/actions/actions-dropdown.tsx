import {
	AlertDialog,
	AlertDialogAction,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
	AlertDialogTrigger,
} from '@/components/shadcnui/alert-dialog';
import { Button } from '@/components/shadcnui/button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/shadcnui/dropdown-menu';
import { ProgrammingActionsConfig } from '@/pages/programing/types/table/index';
import { Copy, Edit, Eye, MoreHorizontal, Trash } from 'lucide-react';

interface ActionsDropdownProps {
	itemId: string;
	title: string;
	config: ProgrammingActionsConfig;
}

export const ActionsDropdown = ({ itemId, title, config }: ActionsDropdownProps) => {
	const { onEdit, onDelete, onView, onDuplicate } = config;

	return (
		<DropdownMenu>
			<DropdownMenuTrigger asChild>
				<Button variant="ghost" size="icon" className="h-8 w-8 rounded-full border border-[#232323] bg-[#232323] p-0 hover:bg-[#232323]/80">
					<MoreHorizontal className="h-4 w-4 text-gray-300" />
				</Button>
			</DropdownMenuTrigger>
			<DropdownMenuContent align="end" className="w-[180px] rounded-lg border border-[#232323] bg-[#181818] p-1 shadow-lg">
				{onView && (
					<DropdownMenuItem
						onClick={() => onView(itemId)}
						className="flex cursor-pointer items-center rounded-md px-3 py-2 text-gray-300 transition-colors hover:bg-[#232323] hover:text-white"
					>
						<Eye className="mr-2 h-4 w-4" />
						<span className="text-sm font-medium">Visualizar</span>
					</DropdownMenuItem>
				)}

				{onEdit && (
					<DropdownMenuItem
						onClick={() => onEdit(itemId)}
						className="flex cursor-pointer items-center rounded-md px-3 py-2 text-gray-300 transition-colors hover:bg-[#232323] hover:text-white"
					>
						<Edit className="mr-2 h-4 w-4" />
						<span className="text-sm font-medium">Editar</span>
					</DropdownMenuItem>
				)}

				{onDuplicate && (
					<DropdownMenuItem
						onClick={() => onDuplicate(itemId)}
						className="flex cursor-pointer items-center rounded-md px-3 py-2 text-gray-300 transition-colors hover:bg-[#232323] hover:text-white"
					>
						<Copy className="mr-2 h-4 w-4" />
						<span className="text-sm font-medium">Duplicar</span>
					</DropdownMenuItem>
				)}

				{onDelete && (
					<>
						<DropdownMenuSeparator className="my-1 bg-[#232323]" />
						<AlertDialog>
							<AlertDialogTrigger asChild>
								<DropdownMenuItem
									onSelect={(e) => e.preventDefault()}
									className="flex cursor-pointer items-center rounded-md px-3 py-2 text-red-500 transition-colors hover:bg-[#232323]"
								>
									<Trash className="mr-2 h-4 w-4" />
									<span className="text-sm font-medium">Excluir</span>
								</DropdownMenuItem>
							</AlertDialogTrigger>
							<AlertDialogContent className="border border-border/30 bg-muted/95 backdrop-blur-sm">
								<AlertDialogHeader>
									<div className="flex items-center gap-2">
										<Trash className="h-6 w-6 text-red-500" />
										<AlertDialogTitle className="text-lg font-semibold text-foreground">Confirmar exclusão</AlertDialogTitle>
									</div>
									<AlertDialogDescription className="mt-2 text-sm text-muted-foreground">
										Você está prestes a excluir a programação <span className="font-semibold text-foreground">"{title}"</span>.
										<br />
										Esta ação <span className="font-semibold text-red-500">não pode ser desfeita</span>. Deseja continuar?
									</AlertDialogDescription>
								</AlertDialogHeader>
								<AlertDialogFooter className="gap-2">
									<AlertDialogCancel className="flex items-center gap-1 border border-border/30 bg-background hover:bg-accent">
										<svg width="16" height="16" fill="none" viewBox="0 0 24 24" className="text-muted-foreground">
											<path stroke="currentColor" strokeWidth="2" d="M6 6l12 12M6 18L18 6" />
										</svg>
										Cancelar
									</AlertDialogCancel>
									<AlertDialogAction
										onClick={() => onDelete(itemId)}
										className="flex items-center gap-1 bg-destructive text-destructive-foreground hover:bg-destructive/90"
									>
										<Trash className="h-4 w-4" />
										Excluir
									</AlertDialogAction>
								</AlertDialogFooter>
							</AlertDialogContent>
						</AlertDialog>
					</>
				)}
			</DropdownMenuContent>
		</DropdownMenu>
	);
};
