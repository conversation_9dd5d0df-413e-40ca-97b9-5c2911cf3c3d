import { IProgramming } from '@/pages/programing/api/requests/find-all';
import { ProgrammingActionsConfig } from '@/pages/programing/types/table/index';
import { createSelectColumn } from '@/pages/presentations/components/table/columns/columns/select-column';
import { createTextColumn } from '@/pages/presentations/components/table/columns/columns/text-column';
import { formatDate } from '@/shared/lib/utils/format-date';
import { ColumnDef } from '@tanstack/react-table';
import { createActionsColumn } from './columns/actions-column';
import { createCountColumn } from './columns/count-column';
import { createDateColumn } from './columns/date-column';

type Props = ProgrammingActionsConfig & {
	customColumns?: ColumnDef<IProgramming>[];
};

export const createProgrammingColumns = ({ onEdit, onDelete, onView, onDuplicate, customColumns = [] }: Props): ColumnDef<IProgramming>[] => [
	createSelectColumn<IProgramming>(),
	createTextColumn<IProgramming>({
		accessorKey: 'id',
		header: 'ID',
		className: 'font-medium text-white',
	}),
	createCountColumn<IProgramming>({
		id: 'devices',
		header: 'Dispositivos',
		accessor: 'id_devices',
		singularLabel: 'dispositivo',
		pluralLabel: 'dispositivos',
	}),
	createCountColumn<IProgramming>({
		id: 'presentations',
		header: 'Apresentações',
		accessor: 'id_presentations',
		singularLabel: 'apresentação',
		pluralLabel: 'apresentações',
	}),
	createDateColumn<IProgramming>({
		accessorKey: 'createdAt',
		header: 'Criado em',
		formatter: formatDate,
	}),
	createDateColumn<IProgramming>({
		accessorKey: 'updatedAt',
		header: 'Atualizado em',
		formatter: formatDate,
	}),
	createActionsColumn<IProgramming>({
		id: 'actions',
		config: { onEdit, onDelete, onView, onDuplicate },
	}),
	...customColumns,
];
