import { Button } from '@/components/shadcnui/button';
import { Table } from '@/components/shadcnui/table';
import { useIsMobile } from '@/hooks/use-is-mobile';
import { IProgramming, IProgrammingFindAllResponse } from '@/pages/programing/api/requests/find-all';
import { useProgrammingTable } from '@/pages/programing/hooks/table/programming-table.hook';
import { CircularProgress, useDisclosure } from '@nextui-org/react';
import { ColumnDef } from '@tanstack/react-table';
import { LucideCalendar } from 'lucide-react';

import { ProgrammingDataTablePagination } from './programming-data-table-pagination';
import { ProgrammingDataTableToolbar } from './programming-data-table-toolbar';
import { ProgrammingTableBody } from './programming-table-body';
import { ProgrammingTableHeader } from './programming-table-header';
import { CreateProgrammingModal } from '../modals/create-programming-modal';

interface IProgrammingDataTableProps {
	readonly columns: ColumnDef<IProgramming>[];
	readonly data: IProgrammingFindAllResponse;
	readonly isLoading?: boolean;
	readonly messageError?: string;
}

export function ProgrammingDataTable({ columns, data, isLoading, messageError }: IProgrammingDataTableProps) {
	const { table } = useProgrammingTable({ data: data.data, columns });
	const isMobile = useIsMobile(640);
	const modalCreateProgramming = useDisclosure();

	console.log(data);

	const renderContent = () => {
		if (isMobile) {
			return (
				<div className="flex flex-col gap-2">
					<div className="mb-2 rounded-lg bg-primary/10 px-3 py-2 text-center text-xs text-primary">
						No mobile, só é possível visualizar as programações existentes.
					</div>
					{messageError ? (
						<div className="py-8 text-center">
							<p className="text-sm font-medium text-red-500">{messageError}</p>
						</div>
					) : isLoading ? (
						<div className="py-8 text-center">
							<CircularProgress className="mx-auto" aria-label="loading..." />
						</div>
					) : (
						data.data.map((programming) => (
							<div key={programming.id} className="rounded-lg border bg-card p-4">
								<div className="flex items-center justify-between">
									<div>
										<h3 className="font-medium">Programação {programming.id}</h3>
										<p className="text-sm text-muted-foreground">
											{programming.id_device.length} dispositivos, {programming.id_presentation.length} apresentações
										</p>
									</div>
								</div>
							</div>
						))
					)}
				</div>
			);
		}

		return (
			<Table>
				<ProgrammingTableHeader table={table} />
				{messageError ? (
					<tbody>
						<tr>
							<td colSpan={columns.length} className="py-16 text-center">
								<p className="text-sm font-medium text-red-500">{messageError}</p>
							</td>
						</tr>
					</tbody>
				) : isLoading ? (
					<tbody>
						<tr>
							<td colSpan={columns.length} className="py-16 text-center">
								<CircularProgress className="mx-auto" aria-label="loading..." />
							</td>
						</tr>
					</tbody>
				) : (
					<ProgrammingTableBody table={table} columns={columns} />
				)}
			</Table>
		);
	};

	return (
		<section className="relative h-full space-y-4 overflow-hidden rounded-xl px-2 py-2 transition-all duration-100 sm:space-y-6 sm:px-4 sm:py-4">
			<ProgrammingDataTableToolbar table={table} />
			<div className="flex w-full flex-col gap-2 sm:flex-row sm:items-center sm:justify-between">
				<div className="space-y-1">
					<p className="text-xs text-muted-foreground sm:text-sm">Gerencie todas as suas programações em um só lugar</p>
				</div>
				<Button
					size="lg"
					className="mt-2 w-full border border-primary bg-black bg-gradient-to-tr from-primary/20 via-primary/10 to-primary/20 font-bold text-primary shadow-sm transition-all duration-200 hover:bg-primary/10 focus:ring-4 focus:ring-primary/30 sm:mt-0 sm:w-auto"
					onClick={modalCreateProgramming.onOpen}
				>
					<LucideCalendar size={22} className="transition-transform duration-300 group-hover:rotate-[15deg] group-hover:scale-110" />
					<span className="ml-2 inline">Nova Programação</span>
				</Button>
			</div>
			<div className={isMobile ? undefined : 'overflow-x-auto rounded-lg border-2 border-white/10'}>
				{renderContent()}
				<ProgrammingDataTablePagination table={table} isMobile={isMobile} />
			</div>

			<CreateProgrammingModal
				onClose={modalCreateProgramming.onClose}
				backdrop="blur"
				isOpen={modalCreateProgramming.isOpen}
				onOpenChange={modalCreateProgramming.onOpenChange}
			/>
		</section>
	);
}
