import { useState } from 'react';
import { createProgrammingColumns } from './components/table/columns/programming-columns';
import { DeleteProgrammingModal } from './components/modals/delete-programming-modal';

import { useDeleteProgramming } from './hooks/delete-programming.hook';
import { useFindAllProgramming } from './hooks/find-all-programming.hook';
import { IProgramming, IProgrammingFindAllResponse } from './api/requests/find-all';
import { ProgrammingDataTable } from './components/table/programming-data-table';

const initialProgrammingData = {
	data: [] as IProgramming[],
	page: 1,
	pageSize: 10,
	total: 0,
	totalPages: 1,
};

export function ProgrammingPage() {
	const { data, isLoading } = useFindAllProgramming();
	const [deleteModalOpen, setDeleteModalOpen] = useState(false);
	const [programmingToDelete, setProgrammingToDelete] = useState<Array<{ id: string; name: string }>>([]);
	const { deleteProgramming, isLoading: isDeleting } = useDeleteProgramming();

	const handleDeleteProgramming = async (id: string) => {
		if (!data?.success) return;
		const programmingIds = id.split(',');
		const currentProgramming = data.data as IProgrammingFindAllResponse;
		const programmingToDelete = programmingIds
			.map((programmingId) => {
				const programming = currentProgramming.data.find((p: IProgramming) => p.id === programmingId);
				return programming ? { id: programmingId, name: `Programação ${programming.id}` } : null;
			})
			.filter((programming): programming is { id: string; name: string } => programming !== null);

		if (programmingToDelete.length > 0) {
			setProgrammingToDelete(programmingToDelete);
			setDeleteModalOpen(true);
		}
	};

	const handleConfirmDelete = async () => {
		try {
			for (const programming of programmingToDelete) {
				await deleteProgramming(programming.id);
			}
			setDeleteModalOpen(false);
			setProgrammingToDelete([]);
		} catch (error) {
			console.error('Erro ao excluir programações:', error);
		}
	};

	const handleCloseDeleteModal = () => {
		setDeleteModalOpen(false);
		setProgrammingToDelete([]);
	};

	const columns = createProgrammingColumns({
		onDelete: handleDeleteProgramming,
		onEdit: undefined,
		onView: undefined,
		onDuplicate: undefined,
	});

	return (
		<>
			<ProgrammingDataTable
				columns={columns}
				data={data?.success ? data.data : initialProgrammingData}
				isLoading={isLoading}
				messageError={!data?.success ? data?.data.message : undefined}
			/>
			<DeleteProgrammingModal
				isOpen={deleteModalOpen}
				onClose={handleCloseDeleteModal}
				onConfirm={handleConfirmDelete}
				programmingName={programmingToDelete.map((p) => p.name).join(', ')}
				isLoading={isDeleting}
			/>
		</>
	);
}

export default ProgrammingPage;
