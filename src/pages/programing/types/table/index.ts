export interface ProgrammingActionsConfig {
	onEdit?: ((id: string) => void) | null;
	onDelete?: ((id: string) => void) | null;
	onView?: ((id: string) => void) | null;
	onDuplicate?: ((id: string) => void) | null;
}

export interface TextColumnOptions<T> {
	accessorKey: keyof T | string;
	header: string;
	className?: string;
	enableSorting?: boolean;
	enableHiding?: boolean;
}

export interface DateColumnOptions<T> {
	accessorKey: keyof T | string;
	header: string;
	formatter: (value: string) => string;
	enableSorting?: boolean;
	enableHiding?: boolean;
}

export interface CountColumnOptions<T> {
	id: string;
	header: string;
	accessor: keyof T;
	singularLabel: string;
	pluralLabel: string;
	enableSorting?: boolean;
	enableHiding?: boolean;
}
